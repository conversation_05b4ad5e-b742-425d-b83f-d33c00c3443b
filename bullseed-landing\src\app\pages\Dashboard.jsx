import React, { useState, useEffect } from 'react';
import TradingChart from '../components/TradingChart';
import BalanceCard from '../components/BalanceCard';
import ReferralCard from '../components/ReferralCard';
import MarketTicker from '../components/MarketTicker';
import cryptoService from '../../services/cryptoService';
import reportService from '../../services/reportService';
import { dbService } from '../../lib/supabase';
import '../styles/Dashboard.css';

const Dashboard = ({ user }) => {
  const [marketData, setMarketData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [transactions, setTransactions] = useState([]);
  const [transactionsLoading, setTransactionsLoading] = useState(true);
  const [downloadingReport, setDownloadingReport] = useState(false);
  const [userStats, setUserStats] = useState({
    spentThisMonth: 0,
    change: 0,
    volume: 0,
    marketCap: 0,
    avgMonthlyGrowth: 0
  });
  const [referralData, setReferralData] = useState({
    totalJoined: 0,
    referralEarn: 0,
    referralLink: ''
  });
  const [investments, setInvestments] = useState([]);
  const [userBalance, setUserBalance] = useState({
    balance: 0,
    earned_funds: 0,
    referral_funds: 0
  });

  // Fetch real market data
  useEffect(() => {
    const fetchMarketData = async () => {
      try {
        setLoading(true);
        const data = await cryptoService.getMarketData();
        setMarketData(data);
      } catch (error) {
        console.error('Error fetching market data:', error);
        // Use fallback data if API fails
        setMarketData(cryptoService.getFallbackMarketData());
      } finally {
        setLoading(false);
      }
    };

    fetchMarketData();

    // Set up real-time updates
    const cleanup = cryptoService.startRealTimeUpdates((data) => {
      setMarketData(data);
    }, 30000); // Update every 30 seconds

    return cleanup;
  }, []);

  // Fetch all user data
  useEffect(() => {
    const fetchUserData = async () => {
      if (!user?.id) {
        console.log('No user ID available');
        setTransactionsLoading(false);
        return;
      }

      try {
        setTransactionsLoading(true);

        // Fetch user data from database
        const userData = await dbService.getUser(user.id);

        // Fetch user transactions
        const userTransactions = await dbService.getTransactions(user.id, 10);
        setTransactions(userTransactions || []);

        // Fetch user investments
        const userInvestments = await dbService.getInvestments(user.id);
        setInvestments(userInvestments || []);

        // Fetch referral data
        const referrals = await dbService.getReferralData(user.id);
        setReferralData({
          totalJoined: referrals.totalJoined,
          referralEarn: referrals.referralEarn,
          referralLink: `https://www.bullseed.com/registration?ref=${userData?.referral_code || user.referral_code || 'default'}`
        });

        // Set user balance data
        if (userData) {
          setUserBalance({
            balance: parseFloat(userData.balance || 0),
            earned_funds: parseFloat(userData.earned_funds || 0),
            referral_funds: parseFloat(userData.referral_funds || 0)
          });
        }

        // Calculate user stats from real data
        calculateUserStats(userTransactions, userInvestments);

      } catch (error) {
        console.error('Error fetching user data:', error);
        // Set empty arrays for new users
        setTransactions([]);
        setInvestments([]);
        setReferralData({
          totalJoined: 0,
          referralEarn: 0,
          referralLink: `https://www.bullseed.com/registration?ref=${user.referral_code || 'default'}`
        });
      } finally {
        setTransactionsLoading(false);
      }
    };

    fetchUserData();
  }, [user]);

  // Calculate user statistics from real data
  const calculateUserStats = (transactions, investments) => {
    const now = new Date();
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();

    // Calculate spent this month from transactions
    const thisMonthTransactions = transactions.filter(t => {
      const transactionDate = new Date(t.created_at);
      return transactionDate.getMonth() === currentMonth &&
             transactionDate.getFullYear() === currentYear &&
             (t.type === 'deposit' || t.type === 'investment') &&
             t.status === 'completed';
    });

    const spentThisMonth = thisMonthTransactions.reduce((sum, t) => sum + parseFloat(t.amount), 0);

    // Calculate total investments
    const totalInvestments = investments.reduce((sum, inv) => sum + parseFloat(inv.amount), 0);

    // Calculate total earnings
    const totalEarnings = investments.reduce((sum, inv) => sum + parseFloat(inv.total_earned || 0), 0);

    // Calculate change percentage (earnings vs investments)
    const changePercentage = totalInvestments > 0 ? ((totalEarnings / totalInvestments) * 100) : 0;

    setUserStats({
      spentThisMonth: spentThisMonth,
      change: changePercentage,
      volume: totalInvestments / 1000000, // Convert to millions for display
      marketCap: (totalInvestments + totalEarnings) / 1000000, // Convert to millions
      avgMonthlyGrowth: totalEarnings / 1000000 // Convert to millions
    });
  };

  // Handle report download
  const handleDownloadReport = async () => {
    try {
      setDownloadingReport(true);
      const result = await reportService.downloadReport(user, transactions, [], marketData);

      if (result.success) {
        // Show success message (you could add a toast notification here)
        console.log(`Report downloaded: ${result.fileName}`);
      } else {
        console.error('Failed to generate report:', result.error);
        alert('Failed to generate report. Please try again.');
      }
    } catch (error) {
      console.error('Error downloading report:', error);
      alert('An error occurred while generating the report.');
    } finally {
      setDownloadingReport(false);
    }
  };

  return (
    <div className="dashboard">
      {/* Market Ticker */}
      <div className="market-ticker-bar">
        <div className="ticker-container">
          <div className="ticker-scroll">
            {marketData.map((coin, index) => (
              <div key={index} className="ticker-item">
                <span className="ticker-symbol">{coin.symbol.split('/')[0]}</span>
                <span className="ticker-price">${coin.price?.toLocaleString() || '0'}</span>
                <span className={`ticker-change ${coin.change >= 0 ? 'positive' : 'negative'}`}>
                  {coin.change >= 0 ? '▲' : '▼'} {Math.abs(coin.change || 0).toFixed(2)}%
                </span>
              </div>
            ))}
            {/* Duplicate for seamless scroll */}
            {marketData.map((coin, index) => (
              <div key={`dup-${index}`} className="ticker-item">
                <span className="ticker-symbol">{coin.symbol.split('/')[0]}</span>
                <span className="ticker-price">${coin.price?.toLocaleString() || '0'}</span>
                <span className={`ticker-change ${coin.change >= 0 ? 'positive' : 'negative'}`}>
                  {coin.change >= 0 ? '▲' : '▼'} {Math.abs(coin.change || 0).toFixed(2)}%
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="dashboard-header">
        <div className="dashboard-welcome">
          <h1>Welcome back, {user.name.split(' ')[0]}</h1>
          <p>Here's a look at your performance and analytics.</p>
        </div>
        <div className="dashboard-header-controls">
          <div className="date-selector">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
              <line x1="16" y1="2" x2="16" y2="6"/>
              <line x1="8" y1="2" x2="8" y2="6"/>
              <line x1="3" y1="10" x2="21" y2="10"/>
            </svg>
            January 2024 - May 2024
          </div>
          <button className="add-new-btn">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <line x1="12" y1="5" x2="12" y2="19"/>
              <line x1="5" y1="12" x2="19" y2="12"/>
            </svg>
            Add new coin
          </button>
        </div>
      </div>

      <div className="dashboard-content">
        {/* Left Column - Stats */}
        <div className="dashboard-stats">
          <div className="stat-card">
            <div className="stat-label">SPENT THIS MONTH</div>
            <div className="stat-value">${userStats.spentThisMonth.toLocaleString()}</div>
            <div className={`stat-change ${userStats.change >= 0 ? 'positive' : 'negative'}`}>
              <span>{userStats.change >= 0 ? '+' : ''}{userStats.change.toFixed(2)}%</span>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-label">VOLUME (24H)</div>
            <div className="stat-value">${(userStats.volume * 1000).toFixed(2)}B</div>
          </div>

          <div className="stat-card">
            <div className="stat-label">MARKET CAP</div>
            <div className="stat-value">${(userStats.marketCap * 1000).toFixed(2)}B</div>
          </div>

          <div className="stat-card">
            <div className="stat-label">AVG MONTHLY GROWTH</div>
            <div className="stat-value">${(userStats.avgMonthlyGrowth * 1000).toFixed(2)}B</div>
          </div>

          <button
            className="download-report-btn"
            onClick={handleDownloadReport}
            disabled={downloadingReport}
          >
            {downloadingReport ? (
              <>
                <div className="loading-spinner-small"></div>
                Generating...
              </>
            ) : (
              <>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                  <polyline points="7,10 12,15 17,10"/>
                  <line x1="12" y1="15" x2="12" y2="3"/>
                </svg>
                Download Report
              </>
            )}
          </button>
        </div>

        {/* Center Column - Chart */}
        <div className="dashboard-chart">
          <div className="trading-chart-header">
            <div className="trading-chart-title">Portfolio Overview</div>
            <div className="trading-chart-info">
              <div className="chart-price-item">
                <div className="chart-price-dot btc"></div>
                <span className="chart-symbol">1BTC</span>
                <span className="price-current">${marketData.find(coin => coin.symbol.includes('BTC'))?.price?.toLocaleString() || '67,684'}</span>
              </div>
              <div className="chart-price-item">
                <div className="chart-price-dot eth"></div>
                <span className="chart-symbol">1ETH</span>
                <span className="price-current">${marketData.find(coin => coin.symbol.includes('ETH'))?.price?.toLocaleString() || '3,754'}</span>
              </div>
            </div>
          </div>

          <div className="chart-container">
            <TradingChart />

            {/* Additional Chart Data Panel */}
            <div className="chart-data-panel">
              <div className="chart-metrics">
                <div className="metric-group">
                  <div className="metric-label">24h Volume</div>
                  <div className="metric-value">{marketData.find(coin => coin.symbol.includes('BTC'))?.volume || '1.26K'}</div>
                </div>
                <div className="metric-group">
                  <div className="metric-label">Market Cap</div>
                  <div className="metric-value">$1.34T</div>
                </div>
                <div className="metric-group">
                  <div className="metric-label">Dominance</div>
                  <div className="metric-value">54.2%</div>
                </div>
              </div>

              <div className="chart-timeframes">
                <button className="timeframe-btn active">1D</button>
                <button className="timeframe-btn">5D</button>
                <button className="timeframe-btn">1M</button>
                <button className="timeframe-btn">3M</button>
                <button className="timeframe-btn">6M</button>
                <button className="timeframe-btn">YTD</button>
                <button className="timeframe-btn">1Y</button>
                <button className="timeframe-btn">All</button>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column - Portfolio Performance */}
        <div className="dashboard-portfolio">
          <div className="portfolio-header">Portfolio Performance</div>
          <div className="portfolio-circle">
            <div className="portfolio-value">{((userStats.avgMonthlyGrowth / userStats.volume) * 100 || 0).toFixed(0)}%</div>
            <div className="portfolio-label">ROI</div>
          </div>
          <div className="portfolio-info">
            <div className="portfolio-date">Last updated: {new Date().toLocaleDateString('en-US', { day: '2-digit', month: 'short' })}</div>
            <div className="portfolio-status">
              {userStats.avgMonthlyGrowth > 0 ? 'Portfolio performing well' : 'Building your portfolio'}
            </div>
          </div>

          <div className="portfolio-bitcoin">
            <div className="bitcoin-info">
              <div className="bitcoin-icon">₿</div>
              <span className="bitcoin-label">Bitcoin</span>
            </div>
            <div className="bitcoin-value">${marketData.find(coin => coin.symbol.includes('BTC'))?.price?.toLocaleString() || '67,684'}</div>
            <div className="bitcoin-change">
              {marketData.find(coin => coin.symbol.includes('BTC'))?.change >= 0 ? '+' : ''}
              {marketData.find(coin => coin.symbol.includes('BTC'))?.change?.toFixed(2) || '+1.2'}%
            </div>
          </div>
        </div>

        {/* Payment History */}
        <div className="payment-history">
          <h3>Payment History</h3>
          {transactionsLoading ? (
            <div className="payment-history-loading">
              <div className="loading-spinner"></div>
              <span>Loading transactions...</span>
            </div>
          ) : (
            <div className="payment-history-table">
              <div className="payment-history-header">
                <div className="payment-col">NAME</div>
                <div className="payment-col">DATE</div>
                <div className="payment-col">PRICE</div>
                <div className="payment-col">STATUS</div>
              </div>
              <div className="payment-history-body">
                {transactions.length === 0 ? (
                  <div className="payment-history-empty">
                    <p>No transactions found</p>
                  </div>
                ) : (
                  transactions.map((transaction) => (
                    <div key={transaction.id} className="payment-history-row">
                      <div className="payment-col">
                        <div className="payment-crypto">
                          <div className={`payment-crypto-icon ${transaction.crypto_currency?.toLowerCase() || 'default'}`}>
                            {transaction.crypto_currency?.charAt(0) || 'T'}
                          </div>
                          <span>{transaction.description || `${transaction.type} Transaction`}</span>
                        </div>
                      </div>
                      <div className="payment-col">
                        {new Date(transaction.created_at).toLocaleDateString('en-US', {
                          day: '2-digit',
                          month: 'short',
                          year: 'numeric'
                        })}
                      </div>
                      <div className="payment-col">${transaction.amount.toLocaleString()}</div>
                      <div className="payment-col">
                        <span className={`payment-status ${
                          transaction.status === 'completed' ? 'success' :
                          transaction.status === 'pending' ? 'pending' : 'failed'
                        }`}>
                          {transaction.status === 'completed' ? 'Success' :
                           transaction.status === 'pending' ? 'Pending' : 'Failed'}
                        </span>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
