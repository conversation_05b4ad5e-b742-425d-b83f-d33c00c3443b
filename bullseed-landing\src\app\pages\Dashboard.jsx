import React, { useState, useEffect } from 'react';
import TradingChart from '../components/TradingChart';
import BalanceCard from '../components/BalanceCard';
import ReferralCard from '../components/ReferralCard';
import MarketTicker from '../components/MarketTicker';
import cryptoService from '../../services/cryptoService';
import reportService from '../../services/reportService';
import { dbService } from '../../lib/supabase';
import '../styles/Dashboard.css';

const Dashboard = ({ user }) => {
  const [marketData, setMarketData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [transactions, setTransactions] = useState([]);
  const [transactionsLoading, setTransactionsLoading] = useState(true);
  const [downloadingReport, setDownloadingReport] = useState(false);
  const [userStats, setUserStats] = useState({
    spentThisMonth: 0,
    change: 0,
    volume: 0,
    marketCap: 0,
    avgMonthlyGrowth: 0
  });
  const [referralData, setReferralData] = useState({
    totalJoined: 0,
    referralEarn: 0,
    referralLink: ''
  });
  const [investments, setInvestments] = useState([]);
  const [userBalance, setUserBalance] = useState({
    balance: 0,
    earned_funds: 0,
    referral_funds: 0
  });

  // Fetch real market data
  useEffect(() => {
    const fetchMarketData = async () => {
      try {
        setLoading(true);
        const data = await cryptoService.getMarketData();
        setMarketData(data);
      } catch (error) {
        console.error('Error fetching market data:', error);
        // Use fallback data if API fails
        setMarketData(cryptoService.getFallbackMarketData());
      } finally {
        setLoading(false);
      }
    };

    fetchMarketData();

    // Set up real-time updates
    const cleanup = cryptoService.startRealTimeUpdates((data) => {
      setMarketData(data);
    }, 30000); // Update every 30 seconds

    return cleanup;
  }, []);

  // Fetch all user data
  useEffect(() => {
    const fetchUserData = async () => {
      if (!user?.id) {
        console.log('No user ID available');
        setTransactionsLoading(false);
        return;
      }

      try {
        setTransactionsLoading(true);

        // Fetch user transactions
        const userTransactions = await dbService.getTransactions(user.id, 10);
        setTransactions(userTransactions || []);

        // Fetch user investments
        const userInvestments = await dbService.getInvestments(user.id);
        setInvestments(userInvestments || []);

        // Fetch referral data
        const referrals = await dbService.getReferralData(user.id);
        setReferralData({
          totalJoined: referrals.totalJoined,
          referralEarn: referrals.referralEarn,
          referralLink: `https://www.bullseed.com/registration?ref=${user.referral_code || 'default'}`
        });

        // Calculate user stats from real data
        calculateUserStats(userTransactions, userInvestments);

      } catch (error) {
        console.error('Error fetching user data:', error);
        // Set empty arrays for new users
        setTransactions([]);
        setInvestments([]);
        setReferralData({
          totalJoined: 0,
          referralEarn: 0,
          referralLink: `https://www.bullseed.com/registration?ref=${user.referral_code || 'default'}`
        });
      } finally {
        setTransactionsLoading(false);
      }
    };

    fetchUserData();
  }, [user]);

  // Fallback transactions for when database is unavailable
  const getFallbackTransactions = () => [
    {
      id: '1',
      type: 'deposit',
      amount: 14923.33,
      status: 'completed',
      crypto_currency: 'ADA',
      description: 'Cardano Deposit',
      created_at: '2024-06-12T00:00:00Z'
    },
    {
      id: '2',
      type: 'investment',
      amount: 2432.90,
      status: 'completed',
      crypto_currency: 'ADA',
      description: 'Premium Plan Investment',
      created_at: '2024-05-16T00:00:00Z'
    },
    {
      id: '3',
      type: 'withdrawal',
      amount: 847.84,
      status: 'pending',
      crypto_currency: 'DGB',
      description: 'Digibyte Withdrawal',
      created_at: '2024-02-21T00:00:00Z'
    },
    {
      id: '4',
      type: 'deposit',
      amount: 1247.90,
      status: 'failed',
      crypto_currency: 'ETH',
      description: 'Ethereum Deposit',
      created_at: '2023-12-19T00:00:00Z'
    }
  ];

  // Handle report download
  const handleDownloadReport = async () => {
    try {
      setDownloadingReport(true);
      const result = await reportService.downloadReport(user, transactions, [], marketData);

      if (result.success) {
        // Show success message (you could add a toast notification here)
        console.log(`Report downloaded: ${result.fileName}`);
      } else {
        console.error('Failed to generate report:', result.error);
        alert('Failed to generate report. Please try again.');
      }
    } catch (error) {
      console.error('Error downloading report:', error);
      alert('An error occurred while generating the report.');
    } finally {
      setDownloadingReport(false);
    }
  };

  const stats = {
    spentThisMonth: 5950.64,
    change: 2.34,
    volume: 84.42,
    marketCap: 804.42,
    avgMonthlyGrowth: 804.42
  };

  const referralData = {
    totalJoined: 0,
    referralEarn: 0,
    referralLink: 'https://www.bullseed.com/registration?ref=9656983838'
  };

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <div className="dashboard-welcome">
          <h1>Welcome back, {user.name.split(' ')[0]}</h1>
          <p>Here's a look at your performance and analytics.</p>
        </div>
      </div>

      <div className="dashboard-content">
        {/* Top Row - Stats and Chart */}
        <div className="dashboard-row">
          <div className="dashboard-stats">
            <div className="stat-card">
              <div className="stat-label">SPENT THIS MONTH</div>
              <div className="stat-value">${stats.spentThisMonth.toLocaleString()}</div>
              <div className="stat-change positive">
                <span className="stat-change-icon">↗</span>
                <span>{stats.change}%</span>
              </div>
            </div>

            <div className="stat-card">
              <div className="stat-label">VOLUME (24H)</div>
              <div className="stat-value">${stats.volume}B</div>
            </div>

            <div className="stat-card">
              <div className="stat-label">MARKET CAP</div>
              <div className="stat-value">${stats.marketCap}B</div>
            </div>

            <div className="stat-card">
              <div className="stat-label">AVG MONTHLY GROWTH</div>
              <div className="stat-value">${stats.avgMonthlyGrowth}B</div>
            </div>

            <button
              className="download-report-btn"
              onClick={handleDownloadReport}
              disabled={downloadingReport}
            >
              {downloadingReport ? (
                <>
                  <div className="loading-spinner-small"></div>
                  Generating...
                </>
              ) : (
                <>
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                    <polyline points="7,10 12,15 17,10"/>
                    <line x1="12" y1="15" x2="12" y2="3"/>
                  </svg>
                  Download Report
                </>
              )}
            </button>
          </div>

          <div className="dashboard-chart">
            <TradingChart />
          </div>

          <div className="dashboard-credit-score">
            <div className="credit-score-header">
              <span>Your credit score</span>
            </div>
            <div className="credit-score-circle">
              <div className="credit-score-value">660</div>
              <div className="credit-score-label">80%</div>
            </div>
            <div className="credit-score-info">
              <div className="credit-score-date">Last Check on 21 Apr</div>
              <div className="credit-score-status">Your credit score is average</div>
            </div>
          </div>
        </div>

        {/* Second Row - Balance and Market Data */}
        <div className="dashboard-row">
          <div className="dashboard-balance">
            <BalanceCard user={user} />
          </div>

          <div className="dashboard-market">
            <div className="market-header">
              <h3>Bitcoin</h3>
              <div className="market-reward">
                <span>Reward 6.2%</span>
              </div>
            </div>
            <div className="market-price">
              <span className="market-currency">BTC</span>
              <span className="market-value">$52,291</span>
              <span className="market-change positive">+6.2%</span>
            </div>
          </div>
        </div>

        {/* Third Row - Referral and Market Ticker */}
        <div className="dashboard-row">
          <div className="dashboard-referral">
            <ReferralCard data={referralData} />
          </div>
        </div>

        <div className="dashboard-row">
          <div className="dashboard-ticker">
            <MarketTicker data={marketData} loading={loading} />
          </div>
        </div>

        {/* Payment History */}
        <div className="dashboard-row">
          <div className="payment-history">
            <h3>Payment History</h3>
            {transactionsLoading ? (
              <div className="payment-history-loading">
                <div className="loading-spinner"></div>
                <span>Loading transactions...</span>
              </div>
            ) : (
              <div className="payment-history-table">
                <div className="payment-history-header">
                  <div className="payment-col">TYPE</div>
                  <div className="payment-col">DATE</div>
                  <div className="payment-col">AMOUNT</div>
                  <div className="payment-col">STATUS</div>
                </div>
                <div className="payment-history-body">
                  {transactions.length === 0 ? (
                    <div className="payment-history-empty">
                      <p>No transactions found</p>
                    </div>
                  ) : (
                    transactions.map((transaction) => (
                      <div key={transaction.id} className="payment-history-row">
                        <div className="payment-col">
                          <div className="payment-crypto">
                            <div className={`payment-crypto-icon ${transaction.crypto_currency?.toLowerCase() || 'default'}`}>
                              {transaction.crypto_currency?.charAt(0) || 'T'}
                            </div>
                            <span>{transaction.description || `${transaction.type} Transaction`}</span>
                          </div>
                        </div>
                        <div className="payment-col">
                          {new Date(transaction.created_at).toLocaleDateString('en-US', {
                            day: '2-digit',
                            month: 'short',
                            year: 'numeric'
                          })}
                        </div>
                        <div className="payment-col">${transaction.amount.toLocaleString()}</div>
                        <div className="payment-col">
                          <span className={`payment-status ${
                            transaction.status === 'completed' ? 'success' :
                            transaction.status === 'pending' ? 'pending' : 'failed'
                          }`}>
                            {transaction.status === 'completed' ? 'Success' :
                             transaction.status === 'pending' ? 'Pending' : 'Failed'}
                          </span>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
