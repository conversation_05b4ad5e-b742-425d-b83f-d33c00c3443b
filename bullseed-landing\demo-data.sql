-- Demo Data for BullSeed Dashboard
-- Run this SQL in your Supabase SQL editor to create demo user and sample data

-- Insert demo user
INSERT INTO users (
    id,
    email,
    name,
    balance,
    earned_funds,
    referral_funds,
    referral_code,
    kyc_status,
    created_at
) VALUES (
    'demo-user-id',
    '<EMAIL>',
    '<PERSON>',
    2500.00,
    1250.75,
    350.25,
    'DEMO123',
    'approved',
    NOW() - INTERVAL '30 days'
) ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    name = EXCLUDED.name,
    balance = EXCLUDED.balance,
    earned_funds = EXCLUDED.earned_funds,
    referral_funds = EXCLUDED.referral_funds,
    referral_code = EXCLUDED.referral_code,
    kyc_status = EXCLUDED.kyc_status;

-- Insert sample transactions
INSERT INTO transactions (
    id,
    user_id,
    type,
    amount,
    status,
    description,
    crypto_currency,
    created_at
) VALUES 
(
    uuid_generate_v4(),
    'demo-user-id',
    'deposit',
    1500.00,
    'completed',
    'Bitcoin Deposit',
    'BTC',
    NOW() - INTERVAL '25 days'
),
(
    uuid_generate_v4(),
    'demo-user-id',
    'investment',
    1000.00,
    'completed',
    'Premium Plan Investment',
    'BTC',
    NOW() - INTERVAL '20 days'
),
(
    uuid_generate_v4(),
    'demo-user-id',
    'deposit',
    800.00,
    'completed',
    'Ethereum Deposit',
    'ETH',
    NOW() - INTERVAL '15 days'
),
(
    uuid_generate_v4(),
    'demo-user-id',
    'earning',
    125.50,
    'completed',
    'Daily Investment Return',
    'BTC',
    NOW() - INTERVAL '10 days'
),
(
    uuid_generate_v4(),
    'demo-user-id',
    'withdrawal',
    500.00,
    'pending',
    'Bitcoin Withdrawal',
    'BTC',
    NOW() - INTERVAL '5 days'
) ON CONFLICT DO NOTHING;

-- Insert sample investment
INSERT INTO investments (
    id,
    user_id,
    plan_id,
    amount,
    daily_return,
    total_expected_return,
    total_earned,
    start_date,
    end_date,
    status
) VALUES (
    uuid_generate_v4(),
    'demo-user-id',
    (SELECT id FROM investment_plans WHERE name = 'Premium' LIMIT 1),
    1000.00,
    64.00,
    1280.00,
    320.00,
    NOW() - INTERVAL '20 days',
    NOW() + INTERVAL '10 days',
    'active'
) ON CONFLICT DO NOTHING;

-- Insert sample referral
INSERT INTO users (
    id,
    email,
    name,
    referred_by,
    created_at
) VALUES (
    'demo-referred-user-id',
    '<EMAIL>',
    'Jane Smith',
    'demo-user-id',
    NOW() - INTERVAL '10 days'
) ON CONFLICT (id) DO UPDATE SET
    referred_by = EXCLUDED.referred_by;

-- Insert referral relationship
INSERT INTO referrals (
    id,
    referrer_id,
    referred_id,
    commission_rate,
    total_earned
) VALUES (
    uuid_generate_v4(),
    'demo-user-id',
    'demo-referred-user-id',
    5.00,
    50.00
) ON CONFLICT DO NOTHING;

-- Insert referral earnings
INSERT INTO referral_earnings (
    id,
    referral_id,
    referrer_id,
    referred_id,
    transaction_id,
    amount,
    commission_amount
) VALUES (
    uuid_generate_v4(),
    (SELECT id FROM referrals WHERE referrer_id = 'demo-user-id' LIMIT 1),
    'demo-user-id',
    'demo-referred-user-id',
    (SELECT id FROM transactions WHERE user_id = 'demo-referred-user-id' AND type = 'deposit' LIMIT 1),
    1000.00,
    50.00
) ON CONFLICT DO NOTHING;

-- Insert sample daily earnings
INSERT INTO daily_earnings (
    id,
    investment_id,
    user_id,
    amount,
    earning_date
) VALUES 
(
    uuid_generate_v4(),
    (SELECT id FROM investments WHERE user_id = 'demo-user-id' LIMIT 1),
    'demo-user-id',
    32.00,
    CURRENT_DATE - INTERVAL '1 day'
),
(
    uuid_generate_v4(),
    (SELECT id FROM investments WHERE user_id = 'demo-user-id' LIMIT 1),
    'demo-user-id',
    32.00,
    CURRENT_DATE - INTERVAL '2 days'
),
(
    uuid_generate_v4(),
    (SELECT id FROM investments WHERE user_id = 'demo-user-id' LIMIT 1),
    'demo-user-id',
    32.00,
    CURRENT_DATE - INTERVAL '3 days'
) ON CONFLICT DO NOTHING;
