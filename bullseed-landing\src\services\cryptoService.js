// CoinGecko API service for real crypto market data
const COINGECKO_API_BASE = 'https://api.coingecko.com/api/v3';

class CryptoService {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 60000; // 1 minute cache
  }

  // Get cached data if available and not expired
  getCachedData(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    return null;
  }

  // Set cache data
  setCachedData(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  // Fetch market data for specific coins
  async getMarketData(coinIds = ['bitcoin', 'ethereum', 'cardano', 'polkadot', 'solana', 'chainlink', 'polygon', 'avalanche-2']) {
    const cacheKey = `market-${coinIds.join(',')}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const response = await fetch(
        `${COINGECKO_API_BASE}/simple/price?ids=${coinIds.join(',')}&vs_currencies=usd&include_24hr_change=true&include_24hr_vol=true&include_market_cap=true`
      );
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      // Transform data to match our component format
      const marketData = Object.entries(data).map(([coinId, coinData]) => {
        const symbolMap = {
          'bitcoin': 'BTC',
          'ethereum': 'ETH',
          'cardano': 'ADA',
          'polkadot': 'DOT',
          'solana': 'SOL',
          'chainlink': 'LINK',
          'polygon': 'MATIC',
          'avalanche-2': 'AVAX'
        };

        return {
          symbol: `${symbolMap[coinId] || coinId.toUpperCase()}/USD`,
          price: coinData.usd,
          change: coinData.usd_24h_change || 0,
          volume: this.formatVolume(coinData.usd_24h_vol || 0),
          marketCap: coinData.usd_market_cap || 0
        };
      });

      this.setCachedData(cacheKey, marketData);
      return marketData;
    } catch (error) {
      console.error('Error fetching market data:', error);
      // Return fallback data if API fails
      return this.getFallbackMarketData();
    }
  }

  // Get trending coins
  async getTrendingCoins() {
    const cacheKey = 'trending';
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const response = await fetch(`${COINGECKO_API_BASE}/search/trending`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      const trending = data.coins.slice(0, 4).map(coin => ({
        symbol: `${coin.item.symbol}/USD`,
        name: coin.item.name,
        price: 0, // Trending endpoint doesn't include price
        change: 0,
        volume: 'N/A',
        rank: coin.item.market_cap_rank
      }));

      this.setCachedData(cacheKey, trending);
      return trending;
    } catch (error) {
      console.error('Error fetching trending coins:', error);
      return [];
    }
  }

  // Get global market stats
  async getGlobalStats() {
    const cacheKey = 'global-stats';
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const response = await fetch(`${COINGECKO_API_BASE}/global`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      const stats = {
        totalMarketCap: data.data.total_market_cap.usd,
        total24hVolume: data.data.total_volume.usd,
        btcDominance: data.data.market_cap_percentage.bitcoin,
        ethDominance: data.data.market_cap_percentage.ethereum,
        activeCryptocurrencies: data.data.active_cryptocurrencies
      };

      this.setCachedData(cacheKey, stats);
      return stats;
    } catch (error) {
      console.error('Error fetching global stats:', error);
      return this.getFallbackGlobalStats();
    }
  }

  // Get specific coin data
  async getCoinData(coinId) {
    const cacheKey = `coin-${coinId}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const response = await fetch(
        `${COINGECKO_API_BASE}/coins/${coinId}?localization=false&tickers=false&market_data=true&community_data=false&developer_data=false&sparkline=false`
      );
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      const coinData = {
        id: data.id,
        symbol: data.symbol.toUpperCase(),
        name: data.name,
        image: data.image.large,
        currentPrice: data.market_data.current_price.usd,
        marketCap: data.market_data.market_cap.usd,
        marketCapRank: data.market_data.market_cap_rank,
        totalVolume: data.market_data.total_volume.usd,
        priceChange24h: data.market_data.price_change_24h,
        priceChangePercentage24h: data.market_data.price_change_percentage_24h,
        circulatingSupply: data.market_data.circulating_supply,
        totalSupply: data.market_data.total_supply,
        maxSupply: data.market_data.max_supply,
        ath: data.market_data.ath.usd,
        athDate: data.market_data.ath_date.usd,
        atl: data.market_data.atl.usd,
        atlDate: data.market_data.atl_date.usd
      };

      this.setCachedData(cacheKey, coinData);
      return coinData;
    } catch (error) {
      console.error(`Error fetching coin data for ${coinId}:`, error);
      return null;
    }
  }

  // Format volume numbers
  formatVolume(volume) {
    if (volume >= 1e9) {
      return `${(volume / 1e9).toFixed(2)}B`;
    } else if (volume >= 1e6) {
      return `${(volume / 1e6).toFixed(2)}M`;
    } else if (volume >= 1e3) {
      return `${(volume / 1e3).toFixed(2)}K`;
    }
    return volume.toFixed(2);
  }

  // Format price with appropriate decimal places
  formatPrice(price) {
    if (price >= 1000) {
      return price.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    } else if (price >= 1) {
      return price.toFixed(4);
    } else {
      return price.toFixed(6);
    }
  }

  // Fallback data when API is unavailable
  getFallbackMarketData() {
    return [
      { symbol: 'BTC/USD', price: 67684, change: -1.01, volume: '1.12B' },
      { symbol: 'ETH/USD', price: 3754.7, change: 0.10, volume: '174.11M' },
      { symbol: 'ADA/USD', price: 0.4567, change: 2.34, volume: '87.11M' },
      { symbol: 'DOT/USD', price: 7.89, change: -0.56, volume: '45.23M' }
    ];
  }

  getFallbackGlobalStats() {
    return {
      totalMarketCap: 2400000000000,
      total24hVolume: 89000000000,
      btcDominance: 54.2,
      ethDominance: 17.8,
      activeCryptocurrencies: 2500
    };
  }

  // Start real-time updates (polling)
  startRealTimeUpdates(callback, interval = 30000) {
    const updateData = async () => {
      try {
        const marketData = await this.getMarketData();
        callback(marketData);
      } catch (error) {
        console.error('Error in real-time update:', error);
      }
    };

    // Initial update
    updateData();

    // Set up polling
    const intervalId = setInterval(updateData, interval);

    // Return cleanup function
    return () => clearInterval(intervalId);
  }
}

export default new CryptoService();
